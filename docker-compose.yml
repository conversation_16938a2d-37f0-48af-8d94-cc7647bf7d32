version: '3.8'

services:
  app:
    image: techpana:latest
    expose:
      - "8000"        # internal port, no host binding
    networks:
      - internal_net
    env_file:
      - .env
    deploy:
      replicas: 3
    labels:
      - "com.docker.compose.project=techpana"  # ← ADD THIS

  haproxy:
    image: haproxy:alpine
    container_name: haproxy_lb
    ports:
      - "8010:80"     # expose host port 8010 → container port 80 (HAProxy)
    volumes:
      - ./haproxy.cfg:/usr/local/etc/haproxy/haproxy.cfg:ro
    networks:
      - internal_net
    labels:
      - "com.docker.compose.project=techpana"  # ← ADD THIS

networks:
  internal_net:
    driver: bridge