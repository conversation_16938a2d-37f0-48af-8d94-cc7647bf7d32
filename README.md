# TechPana RAG API – Quick Start

This guide covers authentication (login), posting news via webhook, and generating reports.

---

Base URL : https://techpana-ai.nextai.asia/v1/

## 1. Login 

Authenticate to ve a Bearer token for all protected endpoints.

**Endpoint:**  
`POST /login`

**cURL Example:**

```sh
curl -X 'POST' \
  'https://techpana-ai.nextai.asia/v1/login' \
  -H 'accept: application/json' \
  -H 'Content-Type: application/x-www-form-urlencoded' \
  -d 'grant_type=password&username=<username>&password=<password>&scope=&client_id=<client_id>'
```

**Sample Response:**

```json
{
  "id": "6857ab3702d3c6d3b16f9180",
  "access_token": "<your_token>",
  "token_type": "bearer",
  "username": "techpana_bot",
  "role": "admin",
  "tenant_id": "684fe1b0fea24acda057ff50",
  "tenant_label": "techpana_bot",
  "tenant_slug": "techpana"
}
```

---

## 2. Webhook – Submit News Post

Send Nepali news posts for processing (cleaning, segmenting, embedding, summary, and Q&A generation).

**Endpoint:**  
`POST /webhook`

**Headers:**  
- `Authorization: Bearer <your_token>`
- `Content-Type: application/json`

**cURL Example:**

```sh
curl -X 'POST' \
  'https://techpana-ai.nextai.asia/v1/webhook' \
  -H 'accept: application/json' \
  -H 'Authorization: Bearer <your_token>' \
  -H 'Content-Type: application/json' \
  -d '[
    {
      "id": "123456",
      "title_nepali": "फास्ट चार्जिङले फोन प्रयोगमा कस्तो असर पार्छ ?",
      "body_nepali": "<p>फास्ट चार्जिङले फोन छिटो चार्ज गर्न सहयोग गर्छ...</p>",
      "category": "प्रविधि",
      "secondary_categories": "मोबाइल, बैट्री",
      "author": "techpana",
      "event_date": "2025-06-19"
    }
  ]'
```

**Sample Response:**

```json
{
  "status": "success",
  "results": [
    {
      "post_id": "123456",
      "result": "Processing started"
    }
  ]
}
```

---

## 3. Generate Report

Retrieve the processed summary, Q&A, and related posts for a given post.

**Endpoint:**  
`GET /get_report/{post_id}`

**cURL Example:**

```sh
curl -X 'GET' \
  'https://techpana-ai.nextai.asia/v1/get_report/123456' \
  -H 'accept: application/json'
```

**Sample Response:**

```json
{
  "post_id": "123456",
  "summary": "Fast charging helps speed up mobile usage...",
  "qa_pairs": [
    {
      "question": "How does fast charging affect battery life?",
      "answer": "It may reduce battery lifespan due to increased heat."
    }
  ],
  "related_posts": [
    {
      "post_id": "abc123",
      "title": "Battery care tips",
      "score": 0.84
    }
  ]
}
```

---

## Notes

- Always use the Bearer token in the `Authorization` header for protected endpoints.
- The `client_id` is your tenant slug or identifier.
- The `rate_limits` have been implemented to control concurrent webhook calls. Each client is allowed a maximum of `10 requests per minute`. Additionally, each webhook request can contain a maximum of `10 documents in the JSON payload`.