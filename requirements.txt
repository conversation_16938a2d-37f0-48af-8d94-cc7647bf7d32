# This file was autogenerated by uv via the following command:
#    uv pip compile pyproject.toml -o requirements.txt
annotated-types==0.7.0
    # via pydantic
anyio==4.8.0
    # via starlette
click==8.1.8
    # via uvicorn
dnspython==2.7.0
    # via pymongo
fastapi==0.115.11
    # via backend-template (pyproject.toml)
h11==0.14.0
    # via uvicorn
idna==3.10
    # via anyio
pydantic==2.10.6
    # via fastapi
pydantic-core==2.27.2
    # via pydantic
pymongo==4.11.2
    # via backend-template (pyproject.toml)
python-dotenv==1.0.1
    # via backend-template (pyproject.toml)
sniffio==1.3.1
    # via anyio
starlette==0.46.1
    # via fastapi
typing-extensions==4.12.2
    # via
    #   anyio
    #   fastapi
    #   pydantic
    #   pydantic-core
uvicorn==0.34.0
    # via backend-template (pyproject.toml)
aiofiles==23.2.1
aiohappyeyeballs==2.6.1     
aiohttp==3.11.18
aiosignal==1.3.2
annotated-types==0.7.0      
anyio==4.8.0
argon2-cffi==25.1.0
argon2-cffi-bindings==21.2.0
asttokens==2.2.1
attrs==23.2.0
Automat==22.10.0
babel==2.17.0
backcall==0.2.0
banks==2.1.2
beautifulsoup4==4.12.3
bleach==6.2.0
bs4==0.0.2
cachetools==5.5.2
certifi==2025.4.26
cffi==1.16.0
charset-normalizer==3.4.2
click==8.1.8
colorama==0.4.6
comm==0.1.4
constantly==23.10.4
contourpy==1.2.1
courlan==1.3.2
cryptography==42.0.5
cssselect==1.2.0
cycler==0.12.1
dataclasses-json==0.6.7
dateparser==1.2.1
debugpy==1.6.7.post1
decorator==5.1.1
defusedxml==0.7.1
Deprecated==1.2.18
dirtyjson==1.0.8
distro==1.9.0
dnspython==2.7.0
doc2text==0.2.4
docs2txt==0.1.6
docx2txt==0.9
et-xmlfile==1.1.0
executing==1.2.0
fastapi==0.115.11
fastjsonschema==2.21.1
filelock==3.13.4
filetype==1.2.0
fonttools==4.53.1
frozenlist==1.6.0
fsspec==2025.3.2
future==1.0.0
google==3.0.0
google-ai-generativelanguage==0.6.15
google-api-core==2.24.2
google-api-python-client==2.169.0
google-auth==2.40.2
google-auth-httplib2==0.2.0
google-generativeai==0.8.5
googleapis-common-protos==1.70.0
greenlet==2.0.2
griffe==1.7.3
grpcio==1.71.0
grpcio-status==1.71.0
h11==0.14.0
h2==4.2.0
hpack==4.1.0
htmldate==1.9.3
httpcore==1.0.9
httplib2==0.22.0
httpx==0.25.2
huggingface-hub==0.30.2
hyperframe==6.1.0
hyperlink==21.0.0
idna==3.10
incremental==22.10.0
iniconfig==2.1.0
ipykernel==6.25.1
ipython==8.14.0
ipywidgets==8.1.7
itemadapter==0.8.0
itemloaders==1.2.0
jedi==0.19.0
Jinja2==3.1.6
jiter==0.9.0
jmespath==1.0.1
joblib==1.4.2
jsonschema==4.24.0
jsonschema-specifications==2025.4.1
jupyter_client==8.3.0
jupyter_core==5.3.1
jupyterlab_pygments==0.3.0
jupyterlab_widgets==3.0.15
jusText==3.0.2
kiwisolver==1.4.5
llama-cloud==0.1.19
llama-cloud-services==0.6.21
llama-index==0.12.34
llama-index-agent-openai==0.4.7
llama-index-cli==0.4.1
llama-index-core==0.12.34.post1
llama-index-embeddings-gemini==0.3.2
llama-index-embeddings-huggingface==0.5.3
llama-index-embeddings-openai==0.3.1
llama-index-indices-managed-llama-cloud==0.6.11
llama-index-llms-gemini==0.4.14
llama-index-llms-openai==0.3.38
llama-index-multi-modal-llms-openai==0.4.3
llama-index-program-openai==0.3.1
llama-index-question-gen-openai==0.3.0
llama-index-readers-file==0.4.7
llama-index-readers-llama-parse==0.4.0
llama-index-vector-stores-qdrant==0.6.0
llama-parse==0.6.21
llama_core==0.4.2
lxml==5.4.0
lxml_html_clean==0.4.2
markdown-it-py==3.0.0
MarkupSafe==3.0.2
marshmallow==3.26.1
matplotlib==3.9.1
matplotlib-inline==0.1.6
mdurl==0.1.2
mime==0.1.0
mistune==3.1.3
motor==3.7.1
mpmath==1.3.0
multidict==6.4.3
mypy_extensions==1.1.0
nbclient==0.10.2
nbconvert==7.16.6
nbformat==5.10.4
nest-asyncio==1.6.0
networkx==3.4.2
nltk==3.9.1
numpy==2.2.6
openai==1.77.0
openpyxl==3.1.2
packaging==23.1
pandas==2.3.0
pandocfilters==1.5.1
parsel==1.9.1
parso==0.8.3
patsy==0.5.6
pickleshare==0.7.5
pillow==10.4.0
platformdirs==4.3.7
pluggy==1.5.0
pocketsphinx==5.0.4
portalocker==2.10.1
prompt-toolkit==3.0.39
propcache==0.3.1
Protego==0.3.1
proto-plus==1.26.1
protobuf==5.29.4
psutil==5.9.5
psycopg2-binary==2.9.7
pure-eval==0.2.2
pyasn1==0.6.0
pyasn1_modules==0.4.0
pycparser==2.22
pydantic==2.10.6
pydantic_core==2.27.2
PyDispatcher==2.0.7
Pygments==2.16.1
PyJWT==2.10.1
pymongo==4.11.2
PyMuPDF==1.25.5
pyOpenSSL==24.1.0
pyparsing==3.1.2
pypdf==5.4.0
PyPDF2==3.0.1
pystata==0.0.1
pytesseract==0.3.13
pytest==7.4.3
python-dateutil==2.9.0.post0
python-docx==0.8.11
python-dotenv==1.0.1
python-multipart==0.0.6
pytz==2025.2
pywin32==306
PyYAML==6.0.2
pyzmq==25.1.1
qdrant-client==1.14.2
queuelib==1.6.2
referencing==0.36.2
regex==2024.11.6
replicate==1.0.6
requests==2.31.0
requests-file==2.0.0
rich==14.0.0
rpds-py==0.25.1
rsa==4.9.1
safetensors==0.5.3
scikit-learn==1.5.1
scipy==1.14.0
Scrapy==2.11.1
seaborn==0.13.2
sentence-transformers==4.1.0
service-identity==24.1.0
six==1.16.0
sniffio==1.3.1
sounddevice==0.5.2
soupsieve==2.5
SpeechRecognition==3.14.3
SQLAlchemy==2.0.20
stack-data==0.6.2
starlette==0.46.1
statsmodels==0.14.2
striprtf==0.0.26
sympy==1.14.0
tenacity==8.5.0
threadpoolctl==3.5.0
tiktoken==0.9.0
tinycss2==1.4.0
tld==0.13
tldextract==5.1.2
tokenizers==0.21.1
torch==2.7.0
tornado==6.3.3
tqdm==4.67.1
trafilatura==2.0.0
traitlets==5.9.0
transformers==4.51.3
Twisted==24.3.0
twisted-iocpsupport==1.0.4
typing-inspect==0.9.0
typing-inspection==0.4.0
typing_extensions==4.12.2
tzdata==2023.3
tzlocal==5.3.1
uritemplate==4.1.1
urllib3==2.2.1
uv==0.7.6
uvicorn==0.34.0
w3lib==2.1.2
wcwidth==0.2.6
webencodings==0.5.1
widgetsnbextension==4.0.14
wrapt==1.17.2
yarl==1.20.0
zope.interface==6.3