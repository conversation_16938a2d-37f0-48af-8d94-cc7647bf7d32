# """ 
# Check if the admin tenant is present , if not create a new admin db with following
# tenants:
# config:
# users:


# tenants: will have the following fields:
# name:
# db_name:
# slug:

# users:
# name:
# hashed_password:
# role:
# (default user with default username and password 
# that will also be used in the individual tenant db as a first user)


# """

# from pymongo import MongoClient
# from dotenv import load_dotenv
# load_dotenv()
# import os

# print(os.getenv("MONGO_URI"))
# print(os.getenv("ADMIN_DB_NAME"))

# client = MongoClient(os.getenv("MONGO_URI"))

# # Get the ADMIN DB
