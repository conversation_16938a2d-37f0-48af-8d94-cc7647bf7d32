from pymongo import MongoClient
from pymongo.database import Database
from dotenv import load_dotenv
import os

load_dotenv()

# Utility: Connect to MongoDB client
def get_client():
    return MongoClient(os.environ.get("MONGO_URI"))

# -------------------------------------------------------------------
# 1. Create or Connect to Admin Database with Collections
# -------------------------------------------------------------------
def setup_admin_db():
    client = get_client()
    project_name = os.environ.get("PROJECT_NAME").lower()
    admin_db_name = f"{project_name}_admin"
    
    # Check if admin db exists already
    print("---x---")
    if admin_db_name in client.list_database_names():
        admin_db = client.get_database(admin_db_name)
        print(f"Admin database '{admin_db_name}' already exists. Connecting to it.")
    else:
        admin_db = client.get_database(admin_db_name)
        print(f"Admin database '{admin_db_name}' does not exist. It will be created upon first collection creation.")
    
    # List of required collections for the admin database
    required_collections = ["tenants", "users", "permissions", "roles", "config"]
    existing_collections = admin_db.list_collection_names()

    # Create any missing collections
    print("---x---")
    for collection in required_collections:
        if collection not in existing_collections:
            admin_db.create_collection(collection)
            print(f"Created collection: {collection}")
    print("---x---")
    return admin_db

# -------------------------------------------------------------------
# 2. Initialize Admin Collections without Overriding User Edits
# -------------------------------------------------------------------
def initialize_admin_collections(admin_db: Database):
    admin_users = admin_db.get_collection("users")
    admin_permissions = admin_db.get_collection("permissions")
    admin_roles = admin_db.get_collection("roles")

    permissions_data = [
        # User Management
        {"name": "invite_user", "description": "Can invite new users", "tags": ["user"]},
        {"name": "create_user", "description": "Can create new users", "tags": ["user"]},
        {"name": "delete_user", "description": "Can delete users", "tags": ["user"]},
        {"name": "read_user_permissions", "description": "Can view user permissions", "tags": ["user", "permissions"]},
        {"name": "reset_user_permissions", "description": "Can reset user permissions", "tags": ["user", "permissions"]},
        {"name": "reset_user_password", "description": "Can reset user passwords", "tags": ["user", "security"]},

        # Permission Management
        {"name": "read_permissions", "description": "Can view all permissions", "tags": ["permissions"]},
        {"name": "create_permission", "description": "Can create new permissions", "tags": ["permissions"]},

        # Role Management
        {"name": "create_role", "description": "Can create new roles", "tags": ["roles"]},
        {"name": "read_roles", "description": "Can view roles", "tags": ["roles"]},
        {"name": "update_role_permissions", "description": "Can update role permissions", "tags": ["roles", "permissions"]},
        {"name": "delete_role", "description": "Can delete roles", "tags": ["roles"]},

        # Configuration Management
        {"name": "create_config", "description": "Can create configuration", "tags": ["config"]},
        {"name": "read_config", "description": "Can read configuration", "tags": ["config"]},
        {"name": "update_config", "description": "Can update configuration", "tags": ["config"]},
        {"name": "delete_config", "description": "Can delete configuration", "tags": ["config"]}
    ]

    roles_data = [
        {
            "name": "admin",
            "default_permissions": [permission["name"] for permission in permissions_data]
        }
    ]

    users_data = [
        {"username": "superadmin","role": "admin","hashed_password": "$argon2id$v=19$m=65536,t=3,p=4$D8iQLmm6tv4RlVsVkK/NCA$YsXH0li/1JhDuLvSjWrrHLsF+/Xqb2xDojdSxF/R8T8", "permissions": [i["default_permissions"] for i in roles_data if i["name"] == "admin"][0]}
    ]

    for permission in permissions_data: admin_permissions.find_one_and_update({"name":permission["name"]}, {"$set": permission}, upsert=True)
    for user in users_data: admin_users.find_one_and_update({"username":user["username"]}, {"$set":user}, upsert=True)
    for role in roles_data: admin_roles.find_one_and_update({"name":role["name"]}, {"$set":role}, upsert=True)
    # Optionally, add similar checks for roles or config if you plan to insert defaults there.

# -------------------------------------------------------------------
# 3. Record a Tenant in the Admin's Tenants Collection
# -------------------------------------------------------------------
def record_tenant_in_admin(admin_db: Database, tenant_name: str, db_name: str, slug: str):
    admin_tenants = admin_db.get_collection("tenants")
    
    if admin_tenants.find_one({"name": tenant_name}):
        raise Exception(f"Tenant '{tenant_name}' already exists in admin database.")
    
    if admin_tenants.find_one({"db_name": db_name}):
        raise Exception(f"Database '{db_name}' already exists in admin database.")
    
    insertion_response = admin_tenants.insert_one({
        "name": tenant_name,
        "db_name": db_name,
        "slug": slug
    })
    print(f"Tenant '{tenant_name}' recorded with ID: {insertion_response.inserted_id}")
    return insertion_response.inserted_id

# -------------------------------------------------------------------
# 4. Create Tenant's Database and Copy Over Values from Admin
# -------------------------------------------------------------------
def create_tenant_database(database_name: str, admin_db: Database):
    client = get_client()

    if database_name in client.list_database_names():
        raise Exception(f"Database with {database_name} already exists!")

    tenant_db = client.get_database(database_name)
    
    # List of collections to create in tenant database
    collections_to_create = ["users", "invitations", "roles", "config", "permissions"]
    existing_tenant_collections = tenant_db.list_collection_names()
    print("---x---")
    
    for collection in collections_to_create:
        if collection not in existing_tenant_collections:
            tenant_db.create_collection(collection)
            print(f"Created tenant collection: {collection}")
    print("---x---")

    # Copy collections from admin to tenant without affecting admin modifications
    try:
        admin_users = admin_db.get_collection("users")
        tenant_db.get_collection("users").insert_many(admin_users.find())
        print("Copied users collection to tenant database.")
    except Exception as e:
        print(f"Users collection not copied: {e}")
    print("---x---")

    try:
        admin_config = admin_db.get_collection("config")
        tenant_db.get_collection("config").insert_many(admin_config.find())
        print("Copied config collection to tenant database.")
    except Exception as e:
        print(f"Config collection not copied: {e}")
    print("---x---")

    try:
        admin_permissions = admin_db.get_collection("permissions")
        tenant_db.get_collection("permissions").insert_many(admin_permissions.find())
        print("Copied permissions collection to tenant database.")
    except Exception as e:
        print(f"Permissions collection not copied: {e}")
    print("---x---")
    
    try:
        admin_roles = admin_db.get_collection("roles")
        tenant_db.get_collection("roles").insert_many(admin_roles.find())
        print("Copied roles collection to tenant database.")
    except Exception as e:
        print(f"Roles collection not copied: {e}")
    print("---x---")
    
    return tenant_db

# -------------------------------------------------------------------
# 5. Orchestration: Add a New Tenant
# -------------------------------------------------------------------
def add_new_tenant(tenant_name: str):
    # Ensure admin database exists and is properly initialized
    admin_db = setup_admin_db()
    initialize_admin_collections(admin_db)

    # Prepare tenant parameters
    tenant_name_clean = tenant_name.lower().replace(" ", "_")
    project_name = os.environ.get("PROJECT_NAME")
    database_name = f"{tenant_name_clean}_{project_name}_db"
    slug = tenant_name_clean

    # Check for and record tenant in the admin database
    tenant_id = record_tenant_in_admin(admin_db, tenant_name, database_name, slug)

    # Create tenant database and copy necessary collections from admin
    tenant_db = create_tenant_database(database_name, admin_db)

    # Notify that the entire process is complete
    print(f"Tenant '{tenant_name}' has been successfully created and configured.")
    return {
        "tenant_id": tenant_id,
        "tenant_name": tenant_name,
        "tenant_db": database_name,
        "tenant_collections": tenant_db.list_collection_names()
    }

# -------------------------------------------------------------------
# Main Orchestration
# -------------------------------------------------------------------
if __name__ == "__main__":
    # Example: Setting up a new tenant called "testdev"
    try:
        result = add_new_tenant("test")
        print("Setup complete:\n", result)
    except Exception as error:
        print("Error during tenant setup:", error)
