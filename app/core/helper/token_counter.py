import logging

logger = logging.getLogger(__name__)
logging.basicConfig(level=logging.INFO)

def price_nep(response):
    pricing = {
        "input_price_per_token": 0.75 * 10 ** -6,
        "output_price_per_token": 0.4 * 10 ** -6,
    }
    try:
        prompt_tokens = response.usage_metadata.prompt_token_count
        completion_tokens = response.usage_metadata.candidates_token_count
        price_usd = (
            prompt_tokens * pricing["input_price_per_token"] +
            completion_tokens * pricing["output_price_per_token"]
        )
        return {
            "price_nep": round(price_usd * 137, 4),
            "prompt_tokens": prompt_tokens,
            "completion_tokens": completion_tokens
        }
    except Exception as e:
        logger.warning(f"Token pricing failed: {e}")
        return {"price_nep": None, "prompt_tokens": None, "completion_tokens": None}