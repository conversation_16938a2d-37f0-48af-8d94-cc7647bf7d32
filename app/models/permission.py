from pydantic import BaseModel, Field, field_validator
from typing import Any, Dict, Literal, List
from enum import Enum


class Permission(BaseModel):
    """
    Permission document from the tenant database -> permissions collection
    """
    id: Any = Field(alias="_id")
    name: str
    description: str
    tags: List[str]


    @field_validator("id")
    def convert_objid_to_str(cls, value):
        return str(value)
    
    @field_validator("name")
    def no_spaces(cls, value):
        val_ = value.replace(" ", "_")
        return val_


class PermissionCreate(BaseModel):
    name: str
    description: str 
    tags: List[str]


class PermissionDelete(BaseModel):
    name: str