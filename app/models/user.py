from pydantic import <PERSON>Model, <PERSON>, field_validator, ConfigDict
from typing import Any, Dict, Literal, List
from pymongo.database import Database
# from src.reply.minio_client import MinIO<PERSON><PERSON>,MinIOConfig

from .role import Role
from .permission import Permission

class User(BaseModel):
    """
    User document from the tenant database -> users collection
    """
    id: Any = Field(alias="_id")
    username: str
    role: Role
    permissions: List[Permission]


    @field_validator("id")
    def convert_objid_to_str(cls, value):
        return str(value)


    model_config = ConfigDict(
        arbitrary_types_allowed=True,
        )
    # class Config:
    #     arbitrary_types_allowed = True

class UserTenantDB(BaseModel):
    tenant_id: str
    db: Database
    user: User

    model_config = ConfigDict(arbitrary_types_allowed=True)

    # class Config:
    #     arbitrary_types_allowed = True

    
class AgentInvitation(BaseModel):
    username: str = Field(
        ...,
        json_schema_extra={"example": "agent_username"}
    )
    role: Literal["admin", "supervisor", "agent"]
    # permissions: Dict[str, bool]| None={"all":True}
    
class AgentRegistration(BaseModel):
    username: str = Field(
        ...,
        json_schema_extra={"example": "agent_username"}
    )
    role: Literal["admin", "supervisor", "agent"]
    password: str = Field(
        ...,
        json_schema_extra={"example": "strongpassword123"}
    )
    token: str = Field(
        ...,
        json_schema_extra={"example": "invitation_token_here"}
    )
