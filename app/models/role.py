from pydantic import BaseModel, Field, field_validator
from typing import Any, Dict, Literal, List
from pymongo.database import Database

class Role(BaseModel):
    """
    User document from the tenant database -> users collection
    """
    id: Any = Field(alias="_id")
    name: str = Field(..., description="Name of the role")
    default_permissions: List[str] = Field(default=[], description="List of permission_ids")
    

    @field_validator("id")
    def convert_objid_to_str(cls, value):
        return str(value)
    

class RoleCreate(BaseModel):
    name: str = Field(..., description="Name of the role")
    default_permissions: List[str] = Field(default=[], description="List of permission_ids")
    

class RoleUpdate(BaseModel):
    default_permissions: List[str] = Field(..., description="Updated list of permission_ids")
    

class RoleDelete(BaseModel):
    id: str
