import pandas as pd
import trafilatura
from bs4 import BeautifulSoup
from typing import List
import logging
import asyncio

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
logger = logging.getLogger(__name__)


class PreProcessor:
    def __init__(self, df: pd.DataFrame):

        try:
            self.df = df.copy()
            logger.info(f"Loaded {len(self.df)} rows")
        except Exception as e:
            logger.error(f"Failed to load CSV file: {e}")
            raise

    def extract_clean_text(self, html: str) -> str:
        text = trafilatura.extract(html)
        return text if text else ""

    def extract_image_urls(self, html: str) -> List[str]:
        soup = BeautifulSoup(html, 'html.parser')
        image_tags = soup.find_all('img')
        return [img.get('src') for img in image_tags if img.get('src')]

    async def process_row(self, i: int, html: str):
        if pd.isna(html):
            logger.warning(f"Row {i}: HTML content is NaN. Skipping.")
            return "", []

        clean_text = await asyncio.to_thread(self.extract_clean_text, html)
        images = await asyncio.to_thread(self.extract_image_urls, html)

        logger.debug(f"Row {i}: Extracted {len(images)} images and {len(clean_text)} characters.")
        return clean_text, images

    async def preprocess(self) -> pd.DataFrame:
        logger.info("Starting async preprocessing...")

        tasks = [
            self.process_row(i, html)
            for i, html in enumerate(self.df["body_nepali"])
        ]
        results = await asyncio.gather(*tasks)

        self.df["clean_content"] = [res[0] for res in results]
        self.df["image_urls"] = [res[1] for res in results]

        logger.info("Preprocessing complete.")
        return self.df
