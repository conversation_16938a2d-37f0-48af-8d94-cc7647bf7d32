from qdrant_client import QdrantClient
from qdrant_client.http import models
from llama_index.vector_stores.qdrant import QdrantVectorStore
from llama_index.embeddings.openai import OpenAIEmbedding
from llama_index.core.schema import Document
from llama_index.core import VectorStoreIndex
from pymongo import AsyncMongoClient
from tqdm import tqdm
from typing import Tuple, List, Dict, Any
import logging
import uuid
import os
from openai import APIError

from app.v1.api.techpana.utilities.config import RAGConfig

logger = logging.getLogger(__name__)
logging.basicConfig(level=logging.INFO)


class VectorStoreManager:
    def __init__(self):
        config = RAGConfig()
        self.client = QdrantClient(  # ✅ Use sync client
            host=config.qdrant_host,
            port=config.qdrant_port,
            https=False,
            timeout=60
        )
        self.collection_name = config.collection_name
        self.embed_model = OpenAIEmbedding(model_name=config.embedding_model)

        self.mongo_uri = config.mongo_uri
        self.mongo_db = config.mongo_db
        self.mongo_collection = config.mongo_collection
        self.mongo_client = AsyncMongoClient(self.mongo_uri)
        self.db = self.mongo_client[self.mongo_db]
        self.collection = self.db[self.mongo_collection]

    def get_vector_store(self) -> QdrantVectorStore:
        return QdrantVectorStore(
            client=self.client,
            collection_name=self.collection_name,
            vector_name="text-dense"
        )

    def get_embedding_model(self):
        return self.embed_model

    def initialize_collection(self, vector_size: int):
        logger.info(f"Initializing Qdrant collection '{self.collection_name}' with vector size {vector_size}")
        self.client.recreate_collection(
            collection_name=self.collection_name,
            vectors_config={
                "text-dense": models.VectorParams(
                    size=vector_size,
                    distance=models.Distance.COSINE
                )
            }
        )

    async def load_data_from_mongo(self, post_ids: List[str]) -> List[Dict]:
        try:
            cursor = self.collection.find({"post_id": {"$in": post_ids}})
            data = await cursor.to_list(length=None)
            logger.info(f"✅ Loaded {len(data)} documents from MongoDB.")
            return data
        except Exception as e:
            logger.error(f"Error loading from MongoDB: {e}")
            return []

    async def populate_vector_store(self, data: List[Dict]) -> bool:
        all_documents = []

        total_items = sum(
            sum(1 for _, text in doc.get("article_content", {}).items() if isinstance(text, str) and text.strip())
            for doc in data
        )

        with tqdm(total=total_items, desc="Creating LlamaIndex Documents") as pbar:
            for entry in data:
                post_id = entry.get("post_id")
                title = entry.get("title_nepali")
                category = entry.get("category")
                url = entry.get("post_url")
                content = entry.get("article_content", {})

                if not any(isinstance(v, str) and v.strip() for v in content.values()):
                    continue

                for section, text in content.items():
                    if isinstance(text, str) and text.strip():
                        doc_id = str(uuid.uuid4())
                        try:
                            doc = Document(
                                text=text.strip(),
                                metadata={
                                    "ref_post_id": post_id,
                                    "category": category,
                                    "title_nepali": title,
                                    "post_url": url,
                                    "section": section,
                                    "level": section,
                                    "doc_type": "content_chunk",
                                    "document_id": doc_id
                                },
                                id_=doc_id
                            )
                            all_documents.append(doc)
                            pbar.update(1)
                        except Exception as e:
                            logger.error(f"Error creating doc {post_id}:{section} — {str(e)}")

        if not all_documents:
            logger.warning("⚠️ No valid documents created.")
            return False

        try:
            index = VectorStoreIndex.from_vector_store(
                self.get_vector_store(), embed_model=self.get_embedding_model()
            )
            batch_size = 100
            for i in range(0, len(all_documents), batch_size):
                batch = all_documents[i:i + batch_size]
                for attempt in range(3):
                    try:
                        index.insert_nodes(batch)
                        logger.info(f"✅ Inserted batch {i // batch_size + 1}")
                        post_ids = list({doc.metadata["ref_post_id"] for doc in batch})
                        await self.collection.update_many(
                            {"post_id": {"$in": post_ids}}, {"$set": {"embedded": True}}
                        )
                        break
                    except Exception as e:
                        logger.error(f"❌ Batch insert failed (attempt {attempt+1}/3): {e}")
                        if attempt == 2:
                            return False
            return True
        except Exception as e:
            logger.error(f"❌ Vector store population failed: {e}")
            return False

    async def setup_and_populate(self, post_ids: List[str], force_recreate: bool = False) -> Tuple[bool, Any]:
        try:
            if force_recreate:
                self.initialize_collection(vector_size=3072)
            else:
                try:
                    self.client.get_collection(self.collection_name)
                except Exception:
                    logger.warning("Collection not found. Initializing new one.")
                    self.initialize_collection(vector_size=3072)

            data = await self.load_data_from_mongo(post_ids=post_ids)
            if not data:
                logger.info("No data to process.")
                return False, None

            success = await self.populate_vector_store(data)
            return success, data if success else None
        except Exception as e:
            logger.error(f"❌ Error in setup_and_populate: {e}")
            return False, None
