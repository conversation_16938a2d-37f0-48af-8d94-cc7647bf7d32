from pymongo import AsyncMongoClient
import time
from fastapi import HTT<PERSON>Ex<PERSON>
from typing import Dict, Any
from app.v1.api.techpana.rag.retrievers.retriever import TraditionalRetriever
from app.v1.api.techpana.utilities.hasher import PostHasher
import logging
logger = logging.getLogger(__name__)
logging.basicConfig(level=logging.INFO)

class ResponseGenerator:
    def __init__(self, mongo_uri: str, mongo_db: str, mongo_collection: str):
        self.mongo_uri = mongo_uri
        self.mongo_db = mongo_db
        self.mongo_collection = mongo_collection
        

        try:
            self.traditional_retriever = TraditionalRetriever()
        except Exception as e:
            logger.error(f"Failed to initialize ResponseGenerator: {str(e)}")
            raise

    async def traditional_retrieve_full(
        self, post_id: str
    ) -> Dict[str, Any]:
        """
        Wrapper to invoke TraditionalRetriever with external MongoDB params.
        """
        try:
            result = await self.traditional_retriever.retrieve_full(
                post_id=post_id,
                mongo_uri=self.mongo_uri,
                mongo_db=self.mongo_db,
                mongo_collection=self.mongo_collection
            )
            return result

        except Exception as e:
            logger.error(f"Error in traditional_retrieve_full for post_id {post_id}: {str(e)}")
            raise

    async def generate_from_post_id(self, post_id: str) -> dict:
        start_time = time.time()

        try:
            result = await self.traditional_retrieve_full(post_id=post_id)
            elapsed_time = time.time() - start_time
            return {
                "post_id": post_id,
                "main_post_summary": result.get("main_post_summary", "No summary generated"),
                "related_posts": result.get("related_posts", []),
                "token_usage" : result.get("token_usage", {}),
                "retrieval_time": elapsed_time
            }


        except Exception as e:
            logger.error(f"Error calling traditional_retrieve_full: {str(e)}")
            return {
                "post_id": post_id,
                "error": f"Error generating response: {str(e)}",
                "retrieval_time": time.time() - start_time
            }

    async def get_report_from_post_id(self, post_id: str) -> dict:
        client = AsyncMongoClient(self.mongo_uri)
        self.collection = client[self.mongo_db][self.mongo_collection]

        hashed_post_id = PostHasher.hash_post_id(post_id)

        try:
            document = await self.collection.find_one(
                {"post_id": hashed_post_id},
                {"_id": 0, "main_post_summary": 1, "related_posts": 1}
            )

            if not document:
                raise HTTPException(status_code=404, detail=f"Post with post_id '{post_id}' not found.")

            # ✅ Append original post_id of current document (optional)
            document["post_id"] = post_id

            # ✅ Inject `post_id` for each related post by parsing it from the `post_url`
            for related in document.get("related_posts", []):
                url = related.get("post_url", "")
                if url:
                    try:
                        related_post_id = url.rstrip("/").split("/")[-1]
                        related["post_id"] = related_post_id
                    except Exception:
                        related["post_id"] = None  # Fallback if parsing fails

            return document

        except Exception as e:
            raise HTTPException(status_code=500, detail=f"Error retrieving report: {str(e)}")
