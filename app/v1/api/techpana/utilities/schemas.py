from dataclasses import dataclass
from typing import Any, Dict

@dataclass
class DocumentNode:
    id_: str
    text: str
    metadata: Dict[str, Any]
    embedding: Any = None

@dataclass
class NodeWithScore:
    node: DocumentNode
    score: float

    @classmethod
    def from_llama_index_node(cls, node):
        return cls(
            node=DocumentNode(
                id_=node.node.node_id,
                text=node.node.text,
                metadata=node.node.metadata,
                embedding=None
            ),
            score=node.score
        )
