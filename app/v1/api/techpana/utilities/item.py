from app.v1.api.techpana.utilities.hasher import PostHasher
from typing import Dict, Any, Mapping
from datetime import datetime


class NewsItem:
    def __init__(self, row: Mapping[str, Any]):
        self.row = row

    def to_structured_dict(self) -> Dict[str, Any]:
        post_id_raw = str(self.row.get("id", ""))
        hashed_id = PostHasher.hash_post_id(post_id_raw)
        content = self.row.get("segmented_content", {})

        return {
            "category": self.row.get("category", ""),
            "post_id": hashed_id,
            "title_nepali": self.row.get("title_nepali", ""),
            "image_urls": self.row.get("image_urls", []),
            "post_url": f"https://www.techpana.com/{post_id_raw}/{post_id_raw}",
            "article_content": content,
            "created_at": datetime.now().isoformat(), 
            "embedded": False,
            "updated": False
        }
