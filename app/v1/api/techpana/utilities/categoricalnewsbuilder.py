from app.v1.api.techpana.utilities.item import NewsItem
from typing import List, Dict, Any
import pandas as pd
import asyncio

class CategoricalNewsBuilder:
    def __init__(self, dataframe: pd.DataFrame):
        self.df = dataframe

    async def process_row(self, i: int, row: pd.Series) -> Dict[str, Any]:
        try:
            news_item = NewsItem(row)
            structured_dict = news_item.to_structured_dict()

            if not structured_dict.get("post_id") or not structured_dict.get("title_nepali"):
                raise ValueError("Missing required fields")

            return structured_dict

        except Exception as e:
            print(f"Error processing row {i}: {e}")
            return None

    async def build(self) -> List[Dict[str, Any]]:
        tasks = [
            self.process_row(i, row)
            for i, row in self.df.iterrows()
        ]
        results = await asyncio.gather(*tasks)
        return [r for r in results if r]

