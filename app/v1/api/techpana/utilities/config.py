from dataclasses import dataclass
from typing import Optional
from llama_index.embeddings.openai import OpenAIEmbedding
from llama_index.vector_stores.qdrant import QdrantVectorStore
from llama_index.core import VectorStoreIndex
from qdrant_client import QdrantClient
from pymongo import MongoClient
import logging
from dotenv import load_dotenv
import os

# Load environment variables from .env file
load_dotenv()

logger = logging.getLogger(__name__)

@dataclass
class RAGConfig:
    # Qdrant configuration parameters
    qdrant_host: str = "*************"
    qdrant_port: int = 6333
    collection_name: str = "techpana_test"
    embedding_model: str = "text-embedding-3-large"
    llm:str = 'gemini-2.5-flash'
    gemini_api_key: str = os.getenv("GOOGLE_API_KEY")
    
    # MongoDB configuration parameters
    mongo_uri: str = os.getenv("MONGO_URI", "")
    mongo_db: str = os.getenv("MONGODB_DB_NAME", "")
    mongo_collection: str = os.getenv("MONGODB_COLLECTION_NAME", "")
    
    # Initialized components
    _qdrant_client: Optional[QdrantClient] = None
    _embed_model: Optional[OpenAIEmbedding] = None
    _vector_store: Optional[QdrantVectorStore] = None
    _index: Optional[VectorStoreIndex] = None
    _mongo_client: Optional[MongoClient] = None

    def __post_init__(self):
        """Initialize components after dataclass initialization."""
        try:
            # Setup Qdrant client
            self._qdrant_client = QdrantClient(
                host=self.qdrant_host,
                port=self.qdrant_port
            )
            
            # Setup embedding model
            self._embed_model = OpenAIEmbedding(
                model_name=self.embedding_model
            )
            
            # Setup vector store
            self._vector_store = QdrantVectorStore(
                client=self._qdrant_client,
                collection_name=self.collection_name
            )
            
            # Create index
            self._index = VectorStoreIndex.from_vector_store(
                self._vector_store,
                embed_model=self._embed_model
            )
            
            # Setup MongoDB client
            self._mongo_client = MongoClient(self.mongo_uri)
            # Verify MongoDB connection
            self._mongo_client.admin.command('ping')
            
            logger.info("RAGConfig initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize RAGConfig: {e}")
            raise

    @property
    def index(self) -> VectorStoreIndex:
        """Get the vector store index."""
        if not self._index:
            raise ValueError("Index not initialized")
        return self._index

    @property
    def qdrant_client(self) -> QdrantClient:
        """Get the Qdrant client."""
        if not self._qdrant_client:
            raise ValueError("Qdrant client not initialized")
        return self._qdrant_client

    @property
    def mongo_client(self) -> MongoClient:
        """Get the MongoDB client."""
        if not self._mongo_client:
            raise ValueError("MongoDB client not initialized")
        return self._mongo_client

    @property
    def mongo_db_name(self) -> str:
        """Get the MongoDB database name."""
        return self.mongo_db

    @property
    def mongo_collection_name(self) -> str:
        """Get the MongoDB collection name."""
        return self.mongo_collection
    