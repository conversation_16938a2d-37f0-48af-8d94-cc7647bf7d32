from fastapi import APIRouter, HTTPException, Depends, Request
from slowapi import Limiter
from slowapi.util import get_remote_address
from app.core.database import get_db_from_tenant_id
from app.models.user import UserTenantDB
from typing import List
from app.core.helper.extended_uid import ExtendedTokenRequest
from app.core.helper.mongo_helper import convert_objectid_to_str
import logging
from app.core.database import get_tenant_id_and_name_from_slug
from app.v1.api.techpana.webhook.models import IncomingPost
from app.v1.api.techpana.webhook.controller import ResponsePopulate
from app.core.security import require_permissions, verify_password, create_access_token
from datetime import datetime, timedelta, timezone

limiter = Limiter(key_func = get_remote_address)

router = APIRouter(prefix = "", tags=["Webhook"])



loggers = logging.getLogger(__name__)


@router.post("/webhook")
@limiter.limit("10/minute") # Limit to 10 requests per minute
async def receive_posts(
    posts: List[IncomingPost],
    request:Request,
    current_user: UserTenantDB = Depends(require_permissions(["write_post"]))
):
            # 🚫 Reject if post list > 10
    if len(posts) > 10:
        raise HTTPException(
            status_code=400,
            detail="Payload too large. You can only send up to 10 posts at a time."
        )
    try:
        # ✅ Initialize the class
        processor = ResponsePopulate()
        results = []
        for post in posts:
            post_data = post.dict()
            result = await processor.process_post_and_generate(post_data)
            results.append({
                "post_id": post.id,
                "result": result
            })
        return {"status": "success", "results": results}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/webhook/trigger-rag", include_in_schema=False)
async def trigger_rag_update(
    current_user: UserTenantDB = Depends(require_permissions(["write_post"]))
):
    """
    Trigger QA + Summary generation on all unprocessed documents in MongoDB.
    """
    try:
        processor = ResponsePopulate()

        await processor.check_post(update_all=True)
        return {"status": "success", "message": "RAG update triggered for all documents."}

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"RAG update failed: {str(e)}")
    

@router.post("/extended_token", include_in_schema=False)
async def get_extended_token(request: ExtendedTokenRequest):
    """Generate an access token with extended validity period.

    Args:
        request: The request containing username, password, client_id, and days for token validity

    Returns:
        A dictionary containing the access token and user information
    """
    # Find the tenant database
    result = get_tenant_id_and_name_from_slug(request.client_id)
    if not result:
        raise HTTPException(status_code=404, detail="Tenant not found")

    tenant_id = str(result["_id"])
    tenant_database = get_db_from_tenant_id(tenant_id)
    

    # Authenticate the user
    user = tenant_database.users.find_one({"username": request.username})
    if not user:
        raise HTTPException(status_code=401, detail="User not found")

    if not verify_password(request.password, user["hashed_password"]):
        raise HTTPException(status_code=401, detail="Incorrect credentials")

    # Generate token with extended validity
    access_token = create_access_token(
        data={"sub": user["username"], "user_id": str(user["_id"]), "role": user["role"], "tenant_id": tenant_id},
        expires_delta=timedelta(days=request.days)
    )

    # Get navigation permissions if available
    try:
        nav_permission = tenant_database.settings.find_one({"name": "nav_permission"}).get(user["role"])
    except Exception:
        nav_permission = None

    # Convert ObjectId to string for JSON serialization
    user = convert_objectid_to_str(user)

    # Log the extended token generation
    loggers.info(f"Extended token generated for user {request.username} with validity of {request.days} days")

    return {
        "id": user["_id"],
        "access_token": access_token,
        "token_type": "bearer",
        "username": user['username'],
        "role": user['role'],
        "tenant_id": tenant_id,
        "tenant_label": result["label"],
        "tenant_slug": request.client_id,
        "nav_permission": nav_permission,
        "token_validity": {
            "days": request.days,
            "hours": 0,
            "minutes": 0,
            "seconds": 0,
            "total_seconds": timedelta(days=request.days).total_seconds()
        },
        "expires_at": (datetime.now(timezone.utc) + timedelta(days=request.days)).isoformat()
    }