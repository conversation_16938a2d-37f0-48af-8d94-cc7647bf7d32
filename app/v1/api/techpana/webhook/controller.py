import logging
import pandas as pd
from typing import Dict
from pymongo import AsyncMongoClient
from app.v1.api.techpana.rag.pipeline.segmentation import NewsSegmenter
from app.v1.api.techpana.rag.pipeline.preprocessor import PreProcessor
from app.v1.api.techpana.utilities.categoricalnewsbuilder import CategoricalNewsBuilder
from app.v1.api.techpana.core import shared_state
from app.v1.api.techpana.utilities.hasher import PostHasher
from app.v1.api.techpana.rag.vector_store import VectorStoreManager

logger = logging.getLogger(__name__)

class ResponsePopulate:
    def __init__(self):
        self.client = AsyncMongoClient(shared_state.rag_config.mongo_uri)
        self.collection = self.client[shared_state.rag_config.mongo_db_name][shared_state.rag_config.mongo_collection_name]
        self.generator = shared_state.generator

    async def generate_qa_and_update(self, post_id: str):
        """
        Generate QA + Summary for a post and update the MongoDB document.
        """
        try:
            result = await self.generator.generate_from_post_id(post_id=post_id)
 
            update_fields = {
                "main_post_summary": result.get("main_post_summary"),
                "related_posts": result.get("related_posts"),
                "token_usage": result.get("token_usage"),
                "updated": True
            }

            await self.collection.update_one(
                {"post_id": post_id},
                {"$set": update_fields}
            )

            logger.info(f"✅ QA and summary updated for post_id: {post_id} with retrival time: {result.get('retrieval_time')}")

        except Exception as e:
            logger.error(f"❌ Failed to generate QA and summary for {post_id}: {e}")

    async def process_incoming_post(self, post_data: Dict):
        """
        Handle incoming post:
        - Check if already processed
        - Preprocess + segment
        - Insert to MongoDB with index and embedded=False
        - Embed and update embedded=True if successful
        - Generate QA + Summary and update MongoDB
        """
        rag_config = shared_state.rag_config
        if not rag_config:
            raise RuntimeError("❌ RAG config not initialized")

        post_id = PostHasher.hash_post_id(str(post_data.get("id")))
    
        if await self.collection.find_one({"post_id": post_id}):
            
            logger.info(f"⚠️ Post {post_id} already exists in MongoDB. Skipping.")
            return {"status": "exists", "message": f"Post {post_id} already processed."}

        # Preprocess
        df = pd.DataFrame([post_data])
        preprocessor = PreProcessor(df=df)
        processed_df = await preprocessor.preprocess()

        # Segment
        segmenter = NewsSegmenter(df=processed_df, api_key=rag_config.gemini_api_key)
        segmented_df = await segmenter.segment(
            user_prompt=(
                "You are a news article segmentation expert. Segment the content "
                "into headline, lead, context, main story, quotes, reaction, and conclusion/call to action."
            )
        )

        # Structure
        builder = CategoricalNewsBuilder(segmented_df)
        structured_news_list = await builder.build()

        # Assign index and embedded
        max_doc = await self.collection.find_one(sort=[("index", -1)])
        current_index = max_doc["index"] if max_doc and "index" in max_doc else 0

        for i, item in enumerate(structured_news_list):
            item["index"] = current_index + i + 1

        # Insert into MongoDB
        await self.collection.insert_many(structured_news_list)
        logger.info(f"✅ Stored {len(structured_news_list)} records for post_id={post_id}")

        # Embedding
        vector_manager = VectorStoreManager()
        post_ids = [item["post_id"] for item in structured_news_list]
        try:
            success, _ = await vector_manager.setup_and_populate(post_ids)
            if success:
                await self.collection.update_many(
                    {"post_id": {"$in": post_ids}},
                    {"$set": {"embedded": True}}
                )
                logger.info("✅ Embedded documents successfully.")
            else:
                logger.warning("⚠️ Embedding failed. Documents remain with embedded=False.")
        except Exception as e:
            logger.error(f"❌ Embedding exception: {e}")

        # Generate QA and update
        try:
            await self.generate_qa_and_update(post_id)
        except Exception as e:
            logger.error(f"❌ Exception during QA generation for post_id {post_id}: {e}")


        return {
            "status": "processed",
            "post_id": post_id,
            "message": "Post processed, segmented, stored, embedded, and QA-generated.",
            "chunks_saved": len(structured_news_list)
        }

    async def process_all_documents_and_update(self):
        """
        Fetch all documents from MongoDB collection,
        generate QA + summary for those missing it,
        and update the documents in MongoDB with a flag.
        """
        print(self.collection.find({}))
        docs_cursor = self.collection.find({})
        all_docs =await docs_cursor.to_list(length=None)
        logger.info(f"📄 Found {len(all_docs)} documents to process.")

        for i, doc in enumerate(all_docs):
            post_id = doc.get("post_id")
            if not post_id:
                logger.warning(f"⚠️ Skipping doc without post_id at index {i}.")
                continue

            if doc.get("main_post_summary") and doc.get("related_posts") and (doc.get("updated") ==  True):
                logger.info(f"⏩ Skipping post_id {post_id}: already processed.")
                continue

            logger.info(f"🔄 Processing [{i+1}/{len(all_docs)}] post_id: {post_id}")

            try:
                result = await self.generator.generate_from_post_id(post_id=post_id)
                update_data = {
                    "main_post_summary": result.get("main_post_summary"),
                    "related_posts": result.get("related_posts"),
                    "updated": True
                }
                await self.collection.update_one(
                    {"post_id": post_id},
                    {"$set": update_data}
                )
                logger.info(f"✅ Successfully updated post_id: {post_id}")
            except Exception as e:
                logger.error(f"❌ Error processing post_id {post_id}: {e}")
                continue

    async def check_post(self, update_all: bool = False):
        """
        If update_all is True, triggers process_all_documents_and_update.
        """
        if update_all:
            await self.process_all_documents_and_update()
            logger.info("✅ Completed QA + summary generation for all documents.")

    async def process_post_and_generate(self, post_data: Dict):
        """
        Combined method to:
        1. Process incoming post (ingest + embed)
        2. Generate QA + summary for it
        """
        result = await self.process_incoming_post(post_data)
        logger.info(f"process_incoming_post result: {result}")
        return result
