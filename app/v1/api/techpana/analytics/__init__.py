from fastapi import APIRouter, Request
from app.v1.api.techpana.analytics.analytics_model import AnalyticsEvent
from app.v1.api.techpana.core import shared_state
from app.v1.api.techpana.utilities.hasher import PostHasher

router = APIRouter(prefix="", tags=["Analytics"])

@router.post("/log-event")
async def log_event(request: Request, event: str, post_id: str = None, parent_post_id: str = None):
    await shared_state.analytics_controller.log_event_from_id(request, event, post_id, parent_post_id)
    return {"status": "logged"}
