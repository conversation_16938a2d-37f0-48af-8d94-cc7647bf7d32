from pymongo import AsyncMongoClient
from app.v1.api.techpana.analytics.analytics_model import AnalyticsEvent
from fastapi import Request
from datetime import datetime, timezone, timedelta
from typing import Optional
from app.v1.api.techpana.utilities.hasher import PostHasher
class AnalyticsController:
    def __init__(self, db_url: str, db_name: str):
        self.client = AsyncMongoClient(db_url)
        self.collection = self.client[db_name]["analytics_logs"]

    async def save_event(self, event: AnalyticsEvent):
        await self.collection.insert_one(event.dict())

    async def log_event_from_id(self, request: Request, event: str, post_id: str = None, parent_post_id: Optional[str] = None):
        ip = request.headers.get("X-Forwarded-For", request.client.host)
        method = request.method
        path = request.url.path
        user_agent = request.headers.get("user-agent")
        hashed_id = PostHasher.hash_post_id(post_id)
        nepal_timezone = timezone(timedelta(hours=5, minutes=45))
        timestamp=datetime.now(nepal_timezone).isoformat()


        analytics_event = AnalyticsEvent(
            ip=ip,
            event=event,
            parent_post_id=parent_post_id,
            post_id=post_id,
            hashed_id=hashed_id,
            path=path,
            method=method,
            user_agent=user_agent,
            timestamp = timestamp
        )
        await self.save_event(analytics_event)