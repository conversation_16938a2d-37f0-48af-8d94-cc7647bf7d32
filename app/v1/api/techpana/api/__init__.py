from fastapi import APIRouter, HTTPException, Request
from app.v1.api.techpana.core import shared_state
import logging
import traceback
from app.v1.api.techpana.core import shared_state


logger = logging.getLogger(__name__)

router = APIRouter(prefix="", tags=["Response"])

@router.get("/get_report/{post_id}")
async def get_report(post_id, request: Request):
    """
    Get report of the requested post_id.
    """
    if not shared_state.generator:
        raise HTTPException(
            status_code=503,
            detail="Generator not initialized. Check server logs."
        )

    try:
        await shared_state.analytics_controller.log_event_from_id(request, event="post_viewed", post_id=post_id)
        return await shared_state.generator.get_report_from_post_id(post_id)
    except Exception as e:
        logger.error(f"❌ Error in /generate: {str(e)}")
        logger.error(traceback.format_exc())
        raise HTTPException(status_code=500, detail=str(f"Cannot get Report for post_id {post_id}"))

