from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware

from .users import router as user_router
from .config import router as config_router
from .roles import router as role_router
from .permissions import router as permissions_router
from app.v1.api.techpana import router as techpana_router

router = FastAPI(
    title="Techpana AI-Reports API",
)

router.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

router.include_router(user_router)
router.include_router(config_router, include_in_schema=False)
router.include_router(role_router, include_in_schema= False)
router.include_router(permissions_router, include_in_schema= False)
router.include_router(techpana_router)