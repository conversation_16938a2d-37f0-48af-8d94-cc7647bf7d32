from fastapi import APIRout<PERSON>, HTTPException, Depends
from bson import ObjectId

from app.core.helper import logger
from app.core.security import get_tenant_info, require_permissions
from app.models.user import UserTenantDB
from app.models.permission import PermissionCreate

loggers = logger.setup_new_logging(__name__)

router = APIRouter(tags=["Permissions"])

@router.get("/permissions")
async def get_all_permissions(
    current_user: UserTenantDB = Depends(require_permissions(["read_permissions"]))
):
    """Get all permissions from the permissions collection"""
    permissions_collection = current_user.db.permissions
    permissions = list(permissions_collection.find({}, {"_id": 0}))
    
    return {"permissions": permissions}

@router.post("/permissions")
async def create_permission(
    permission: PermissionCreate,
    current_user: UserTenantDB = Depends(require_permissions(["create_permission"]))
):
    """Create a new permission"""
    permissions_collection = current_user.db.permissions
    
    # Check if permission with same name already exists
    existing_permission = permissions_collection.find_one({"name": permission.name})
    if existing_permission:
        raise HTTPException(
            status_code=400,
            detail=f"Permission with name '{permission.name}' already exists"
        )
    
    # Create new permission document
    permission_doc = permission.model_dump()
    
    try:
        result = permissions_collection.insert_one(permission_doc)
        # Remove _id from response
        permission_doc.pop("_id", None)
        loggers.info(f"Created new permission: {permission.name}")
        return permission_doc
    except Exception as e:
        loggers.error(f"Error creating permission: {str(e)}")
        raise HTTPException(status_code=500, detail="Error creating permission")