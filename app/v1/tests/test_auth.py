import os
import sys
import pytest
from fastapi.testclient import TestClient
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add project root to Python path
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(os.path.dirname(current_dir)))
if project_root not in sys.path:
    sys.path.append(project_root)

from main import app  # Import directly from main.py at project root

@pytest.fixture
def client():
    return TestClient(app)

class TestLoginEndpoint:
    LOGIN_URL = "/login"
    DEFAULT_CLIENT_ID = "test"
    TEST_USERNAME = os.getenv("TEST_USERNAME")
    TEST_PASSWORD = os.getenv("TEST_PASSWORD")

    def test_successful_login(self, client):
        response = client.post(
            self.LOGIN_URL,
            data={
                "username": self.TEST_USERNAME,
                "password": self.TEST_PASSWORD,
                "client_id": self.DEFAULT_CLIENT_ID
            }
        )
        print(response.json())
        assert response.status_code == 200
        assert "access_token" in response.json()
        assert "token_type" in response.json()
        assert response.json()["token_type"] == "bearer"

    # Add new tests for client_id scenarios
    def test_missing_client_id(self, client):
        response = client.post(
            self.LOGIN_URL,
            data={
                "username": "superadmin",
                "password": "godmod"
            }
        )
        print(response.json())
        assert response.status_code == 422

    def test_invalid_client_id(self, client):
        response = client.post(
            self.LOGIN_URL,
            data={
                "username": "superadmin",
                "password": "godmod",
                "client_id": "invalid_client"
            }
        )
        assert response.status_code == 401
        # assert response.json()["detail"] == "Invalid client"

    def test_empty_client_id(self, client):
        response = client.post(
            self.LOGIN_URL,
            data={
                "username": "superadmin",
                "password": "godmod",
                "client_id": ""
            }
        )
        assert response.status_code == 401

    # Update existing tests to include client_id
    def test_invalid_credentials(self, client):
        response = client.post(
            self.LOGIN_URL,
            data={
                "username": "wronguser",
                "password": "wrongpass",
                "client_id": self.DEFAULT_CLIENT_ID
            }
        )
        assert response.status_code == 401
        # assert response.json()["detail"] == "Invalid credentials"

    def test_empty_credentials(self, client):
        response = client.post(
            self.LOGIN_URL,
            data={
                "username": "",
                "password": "",
                "client_id": self.DEFAULT_CLIENT_ID
            }
        )
        assert response.status_code == 401

    def test_missing_credentials(self, client):
        response = client.post(
            self.LOGIN_URL,
            data={"client_id": self.DEFAULT_CLIENT_ID}
        )
        assert response.status_code == 422

    def test_missing_password(self, client):
        response = client.post(
            self.LOGIN_URL,
            data={
                "username": "testuser",
                "client_id": self.DEFAULT_CLIENT_ID
            }
        )
        assert response.status_code == 422

    def test_missing_username(self, client):
        response = client.post(
            self.LOGIN_URL,
            data={
                "password": "testpass123",
                "client_id": self.DEFAULT_CLIENT_ID
            }
        )
        assert response.status_code == 422

    def test_invalid_json(self, client):
        response = client.post(
            self.LOGIN_URL,
            data="invalid json"
        )
        assert response.status_code == 422

    def test_wrong_http_method(self, client):
        response = client.get(self.LOGIN_URL)
        assert response.status_code == 405

    def test_long_username(self, client):
        response = client.post(
            self.LOGIN_URL,
            data={
                "username": "a" * 101,  # Assuming max length is 100
                "password": "testpass123"
            }
        )
        assert response.status_code == 422

    def test_long_password(self, client):
        response = client.post(
            self.LOGIN_URL,
            data={
                "username": "testuser",
                "password": "a" * 101  # Assuming max length is 100
            }
        )
        assert response.status_code == 422

    def test_special_characters_username(self, client):
        response = client.post(
            self.LOGIN_URL,
            data={
                "username": "test@#$%^&*()",
                "password": "testpass123"
            }
        )
        assert response.status_code == 422

    def test_sql_injection_attempt(self, client):
        response = client.post(
            self.LOGIN_URL,
            data={
                "username": "' OR '1'='1",
                "password": "' OR '1'='1"
            }
        )
        assert response.status_code == 422

    def test_content_type_header(self, client):
        response = client.post(
            self.LOGIN_URL,
            headers={"Content-Type": "text/plain"},
            data={
                "username": "testuser",
                "password": "testpass123"
            }
        )
        assert response.status_code == 422